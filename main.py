#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
✨ Python工具百宝箱 ✨
作者: Claude AI
日期: 2023年
描述: 一个多功能Python工具集合，包含文件批量分配和图片加白底等实用功能
"""

import sys
import os
import logging
import json
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import messagebox
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding

# 设置默认编码为UTF-8
import locale
locale.getpreferredencoding = lambda: 'UTF-8'

# 安全地设置编码，兼容不同Python版本
try:
    if hasattr(sys.stdout, 'buffer'):
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer)
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer)
except (AttributeError, TypeError):
    pass

from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QVBoxLayout, 
                            QWidget, QTextEdit, QStyleFactory, QMessageBox)
from PyQt5.QtGui import QFont, QColor, QTextCursor, QPalette, QIcon
from PyQt5.QtCore import Qt, QSettings

# 导入功能模块
from 模块.程序快捷启动 import ProgramLauncher as 程序快捷启动
from 模块.文件批量分配 import FileDistribution as 文件批量分配
from 模块.图片加白底 import AddWhiteBackground as 图片加白底
from 模块.中文文件删除 import ChineseFileRemover as 中文文件删除
from 模块.图片分类 import ImageClassifier as 图片分类
from 模块.图片筛选 import ImageFilter as 图片筛选

# 全局配置
CONFIG_FILE = "config.json"

def validate_key():
    """验证 key.vdf 文件的有效性"""
    key_file = "key.vdf"
    if not os.path.exists(key_file):
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", f"未找到授权文件 {key_file}")
        root.destroy()
        exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    with open(key_file, "rb") as f:
        data = f.read()
        iv = data[:16]
        ciphertext = data[16:]

    # 解密数据
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
    decryptor = cipher.decryptor()
    try:
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()
    except ValueError:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", "解密失败：数据可能被篡改")
        root.destroy()
        exit(1)

    # 移除填充
    unpadder = padding.PKCS7(128).unpadder()
    try:
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()
    except ValueError:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", "数据校验失败：填充错误")
        root.destroy()
        exit(1)

    # 验证时间有效性
    try:
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))
    except ValueError:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", "时间格式无效")
        root.destroy()
        exit(1)

    # 有效期验证（30天）
    if datetime.now() - stored_time > timedelta(days=30):
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("错误", "软件授权已过期")
        root.destroy()
        exit(1)

class LogTextBox(QTextEdit):
    """自定义日志文本框，用于显示彩色日志"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setReadOnly(True)
        # 使用更现代的字体
        self.setFont(QFont("微软雅黑", 9))
        # 设置样式表
        self.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
            }
        """)
        
    def append_log(self, message, level="INFO"):
        """添加带颜色的日志"""
        color_map = {
            "INFO": QColor(0, 0, 0),         # 黑色
            "SUCCESS": QColor(40, 167, 69),  # 绿色
            "WARNING": QColor(255, 165, 0),  # 橙色
            "ERROR": QColor(220, 53, 69)     # 红色
        }
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{current_time}] [{level}] {message}"
        
        self.setTextColor(color_map.get(level, QColor(0, 0, 0)))
        self.append(formatted_message)
        self.moveCursor(QTextCursor.End)

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        # 加载配置
        self.config = self.load_config()
        self.init_ui()
        
    def init_ui(self):
        """初始化主窗口界面"""
        # 设置窗口标题和大小
        self.setWindowTitle("上班工具百宝箱")
        self.setGeometry(100, 100, 900, 650)
        
        # 设置应用样式
        self.set_application_style()
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建选项卡部件
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #ddd;
                border-radius: 4px;
                background-color: #fff;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-bottom: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                padding: 8px 16px;
                margin-right: 2px;
                font-family: '微软雅黑';
                font-size: 10pt;
            }
            QTabBar::tab:selected {
                background-color: #fff;
                border-bottom: 1px solid #fff;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e9ecef;
            }
        """)
        
        # 创建日志文本框
        self.log_box = LogTextBox()
        self.log_box.setMaximumHeight(150)
        
        # 添加功能选项卡
        self.add_feature_tabs()
        
        # 将部件添加到布局
        main_layout.addWidget(self.tabs, 7)
        main_layout.addWidget(self.log_box, 3)
        
        # 初始化日志
        self.init_logging()
        
    def set_application_style(self):
        """设置应用程序样式"""
        # 使用Fusion风格作为基础
        QApplication.setStyle(QStyleFactory.create("Fusion"))
        
        # 创建自定义调色板
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(255, 255, 255))
        palette.setColor(QPalette.WindowText, QColor(0, 0, 0))
        palette.setColor(QPalette.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.AlternateBase, QColor(245, 245, 245))
        palette.setColor(QPalette.ToolTipBase, QColor(255, 255, 255))
        palette.setColor(QPalette.ToolTipText, QColor(0, 0, 0))
        palette.setColor(QPalette.Text, QColor(0, 0, 0))
        palette.setColor(QPalette.Button, QColor(240, 240, 240))
        palette.setColor(QPalette.ButtonText, QColor(0, 0, 0))
        palette.setColor(QPalette.BrightText, QColor(255, 0, 0))
        palette.setColor(QPalette.Highlight, QColor(66, 133, 244))
        palette.setColor(QPalette.HighlightedText, QColor(255, 255, 255))
        
        # 设置应用程序调色板
        app = QApplication.instance()
        app.setPalette(palette)
        
        # 设置全局样式表
        app.setStyleSheet("""
            QWidget {
                font-family: '微软雅黑', 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 10pt;
            }
            QPushButton {
                background-color: #f8f9fa;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 6px 12px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
            }
            QPushButton:pressed {
                background-color: #ddd;
            }
            QLineEdit, QSpinBox {
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 4px;
                min-height: 25px;
            }
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                min-height: 20px;
            }
            QProgressBar::chunk {
                background-color: #66b2ff;
                width: 20px;
            }
            QLabel {
                font-size: 10pt;
            }
            QCheckBox {
                spacing: 8px;
            }
        """)
        
    def add_feature_tabs(self):
        """添加各个功能的选项卡"""
        # 程序快捷启动选项卡（放在第一位）
        program_launcher_tab = 程序快捷启动(self)
        self.tabs.addTab(program_launcher_tab, "程序快捷启动")
        
        # 文件批量分配选项卡
        file_distribution_tab = 文件批量分配(self)
        self.tabs.addTab(file_distribution_tab, "文件批量分配")
        
        # 图片加白底选项卡
        add_white_bg_tab = 图片加白底(self)
        self.tabs.addTab(add_white_bg_tab, "图片加白底")
        
        # 中文文件删除选项卡
        chinese_file_remover_tab = 中文文件删除(self)
        self.tabs.addTab(chinese_file_remover_tab, "中文文件删除")
        
        # 图片分类选项卡
        image_classifier_tab = 图片分类(self)
        self.tabs.addTab(image_classifier_tab, "图片分类")

        # 图片筛选选项卡
        image_filter_tab = 图片筛选(self)
        self.tabs.addTab(image_filter_tab, "图片筛选")
    
    def init_logging(self):
        """初始化日志系统"""
        # 配置日志处理器
        class LogHandler(logging.Handler):
            def __init__(self, log_box):
                super().__init__()
                self.log_box = log_box
                
            def emit(self, record):
                level = record.levelname
                msg = self.format(record)
                
                if level == "INFO":
                    self.log_box.append_log(msg, "INFO")
                elif level == "WARNING":
                    self.log_box.append_log(msg, "WARNING")
                elif level == "ERROR":
                    self.log_box.append_log(msg, "ERROR")
                elif level == "CRITICAL":
                    self.log_box.append_log(msg, "ERROR")
                elif level == "DEBUG":
                    self.log_box.append_log(msg, "INFO")
                else:
                    self.log_box.append_log(msg, "INFO")
        
        # 配置根日志记录器
        logger = logging.getLogger()
        logger.setLevel(logging.INFO)
        
        # 清除现有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 添加自定义处理器
        handler = LogHandler(self.log_box)
        formatter = logging.Formatter('%(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # 输出欢迎信息
        logging.info("欢迎使用Python工具百宝箱！")
        logging.info("程序已成功启动，请选择功能选项卡开始使用。")
    
    def load_config(self):
        """加载配置文件"""
        config = {
            "program_launcher": {
                "programs": []
            },
            "file_distribution": {
                "default_name": "吴胜雄",
                "default_count": 1000,
                "last_folder": "",
                "last_move_folder": ""
            },
            "add_white_bg": {
                "last_folder": "",
                "keep_original": False
            },
            "chinese_file_remover": {
                "last_folder": "",
                "auto_delete": False
            },
            "image_classifier": {
                "last_folder": "",
                "threshold": 200
            },
            "image_filter": {
                "last_folder": "",
                "delete_videos": False,
                "delete_horizontal": False,
                "delete_vertical": False
            }
        }
        
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 更新默认配置
                    for key, value in loaded_config.items():
                        if key in config:
                            config[key].update(value)
                    logging.info("配置文件加载成功")
        except Exception as e:
            logging.error(f"加载配置文件出错: {str(e)}")
        
        return config
    
    def save_config(self):
        """保存配置到文件"""
        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            logging.info("配置已保存")
        except Exception as e:
            logging.error(f"保存配置文件出错: {str(e)}")
    
    def closeEvent(self, event):
        """窗口关闭事件，保存配置"""
        self.save_config()
        event.accept()

def get_run_directory():
    """获取程序运行目录，兼容打包后的exe"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的exe
        return os.path.dirname(sys.executable)
    else:
        # 如果是直接运行的py文件
        return os.path.dirname(os.path.abspath(__file__))

if __name__ == "__main__":
    # 设置工作目录为程序运行目录
    os.chdir(get_run_directory())
    
    # 验证授权
    validate_key()
    
    # 创建应用
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_()) 