# 图片分辨率修改功能说明

## 功能概述
新增的"图片分辨率修改"功能可以批量修改图片的分辨率，支持自定义分辨率和多种缩放模式。

## 主要特性

### 1. 自定义分辨率
- 支持设置任意宽度和高度（1-10000像素）
- 提供常用分辨率快捷按钮：
  - 1920x1080 (Full HD)
  - 1280x720 (HD)
  - 1024x768 (XGA)
  - 800x600 (SVGA)

### 2. 多种缩放模式
- **适应模式**：保持图片比例，裁剪多余部分以完全填充目标尺寸
- **填充模式**：保持图片比例，用白色填充空白区域
- **拉伸模式**：直接拉伸到目标尺寸（可能导致变形）

### 3. 配置持久化
- 自动保存上次使用的文件夹路径
- 记住分辨率设置
- 保存缩放模式和其他选项
- 程序重启后自动恢复上次的设置

### 4. 安全选项
- **保留原图**：处理后的图片保存为新文件，原图不被覆盖
- 新文件命名格式：`原文件名_宽度x高度.扩展名`

### 5. 支持的图片格式
- PNG
- JPG/JPEG
- BMP
- GIF
- TIFF
- WebP

## 使用方法

1. **选择图片文件夹**：点击"浏览..."按钮选择包含图片的文件夹
2. **设置分辨率**：
   - 手动输入宽度和高度
   - 或点击常用分辨率快捷按钮
3. **选择缩放模式**：根据需要选择适合的缩放模式
4. **配置选项**：
   - 勾选"保留原图"以避免覆盖原文件
5. **开始处理**：点击"开始处理"按钮
6. **监控进度**：通过进度条和状态信息了解处理进度
7. **取消操作**：如需中断，点击"取消处理"按钮

## 界面风格
新功能完全遵循现有程序的界面风格：
- 使用相同的颜色方案和字体
- 保持一致的布局和控件样式
- 集成到主程序的选项卡系统中
- 使用相同的日志系统显示处理状态

## 技术特点
- 多线程处理，不阻塞用户界面
- 高质量图片缩放算法（LANCZOS）
- 内存优化，逐个处理图片文件
- 完善的错误处理和用户反馈
- 支持中断和恢复操作

## 注意事项
- 处理大量图片时请耐心等待
- 建议在处理重要图片前先勾选"保留原图"选项
- 不同缩放模式会产生不同的视觉效果，请根据需要选择
- 程序会自动保存所有设置，下次使用时无需重新配置
